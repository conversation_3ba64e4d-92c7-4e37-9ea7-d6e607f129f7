<?php namespace Phpcmf\Controllers\Admin;

/**
 * UEditorPlus配置管理
 */
class Config extends \Phpcmf\Admin
{

    public function __construct() {
        parent::__construct();
        \Phpcmf\Service::L('input')->class_name = strtolower(APP_DIR);
    }

    /**
     * 配置页面
     */
    public function index() {

        $page = max(0, (int)\Phpcmf\Service::L('input')->get('page'));

        if (IS_POST) {
            $data = \Phpcmf\Service::L('input')->post('data');
            \Phpcmf\Service::M()->table(SITE_ID.'_config')->update(0, array(
                'value' => dr_array2string($data)
            ), '`name`="ueditorplus_config"');
            \Phpcmf\Service::L('cache')->sync_cache('');
            $this->_json(1, dr_lang('操作成功'));
        }

        // 获取配置数据
        $data = [];
        $config = \Phpcmf\Service::M()->table(SITE_ID.'_config')->where('name', 'ueditorplus_config')->getRow();
        if ($config && $config['value']) {
            $data = dr_string2array($config['value']);
        }

        // 默认配置
        if (!$data) {
            $data = [
                'ak' => '', // 百度地图AK
                'ai_enabled' => 1, // AI功能开关
                'ai_api_key' => '', // AI API密钥
                'formula_enabled' => 1, // 公式功能开关
                'chart_enabled' => 1, // 图表功能开关
                'template_enabled' => 1, // 模板功能开关
                'audio_enabled' => 1, // 音频功能开关
                'video_max_size' => 100, // 视频最大尺寸(MB)
                'image_max_size' => 10, // 图片最大尺寸(MB)
                'file_max_size' => 50, // 文件最大尺寸(MB)
                'watermark_enabled' => 0, // 水印功能
                'auto_save' => 1, // 自动保存
                'paste_filter' => 1, // 粘贴过滤
            ];
        }

        $form = dr_form_hidden(['page' => $page]);

        \Phpcmf\Service::V()->assign([
            'form' => $form,
            'data' => $data,
            'page' => $page,
        ]);
        \Phpcmf\Service::V()->display('config.html');
    }

    /**
     * 一键设置
     */
    public function set_index() {
        $id = (int)\Phpcmf\Service::L('input')->get('id');
        
        if ($id == 1) {
            // 启用UEditorPlus
            $this->_enable_ueditorplus();
            $this->_json(1, dr_lang('已成功设置全站使用UEditorPlus编辑器'));
        } else {
            // 还原系统编辑器
            $this->_disable_ueditorplus();
            $this->_json(1, dr_lang('已成功还原为系统编辑器'));
        }
    }

    /**
     * 启用UEditorPlus
     */
    private function _enable_ueditorplus() {
        // 这里可以添加启用UEditorPlus的逻辑
        // 比如修改字段配置等
    }

    /**
     * 禁用UEditorPlus
     */
    private function _disable_ueditorplus() {
        // 这里可以添加禁用UEditorPlus的逻辑
        // 比如还原为系统默认编辑器
    }

    /**
     * 同步文件到多站
     */
    public function sync_files() {
        // 同步UEditorPlus文件到多站点
        $sites = \Phpcmf\Service::M()->table('site')->getAll();
        $success = 0;
        
        foreach ($sites as $site) {
            if ($site['id'] != SITE_ID) {
                // 复制文件到其他站点
                $source = ROOTPATH . 'api/ueditorplus/';
                $target = ROOTPATH . 'api/' . $site['domain'] . '/ueditorplus/';
                
                if (dr_dir_copy($source, $target)) {
                    $success++;
                }
            }
        }
        
        $this->_json(1, dr_lang('成功同步到 %s 个站点', $success));
    }

}
