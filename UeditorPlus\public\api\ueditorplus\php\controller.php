<?php

defined('BASEPATH') OR exit('No direct script access allowed');

header('Access-Control-Allow-Origin:*');
header('Access-Control-Allow-Headers: X-Requested-With,X_Requested_With');

chdir(__DIR__);

// 加载UEditorPlus配置
if (is_file(ROOTPATH."api/ueditorplus/php/config.php")) {
    $CONFIG = require ROOTPATH."api/ueditorplus/php/config.php";
} else {
    echo json_encode(array(
        'state'=> '无权限访问api/ueditorplus/php/config.php文件'
    ), JSON_UNESCAPED_UNICODE);
    exit;
}

// 设置图片标题标签
if (isset($CONFIG['imageAltValue']) && $CONFIG['imageAltValue'] == 'name') {
    $CONFIG["imgTitleTag"] = '';
} else {
    $CONFIG["imgTitleTag"] = defined('UEDITOR_IMG_TITLE') ? UEDITOR_IMG_TITLE : '';
}

$action = $_GET['action'];

// 检查上传权限
$error = $this->_check_upload_auth(1);
if (!$error) {
    // 验证了才能上传
    switch ($action) {
        case 'config':
            $result = json_encode($CONFIG, JSON_UNESCAPED_UNICODE);
            break;

        /* 上传图片 */
        case 'image':
        case 'uploadimage':
            /* 上传涂鸦 */
        case 'scrawl':
        case 'uploadscrawl':
            /* 上传截图 */
        case 'snap':
        case 'uploadsnap':
            /* 上传视频 */
        case 'video':
        case 'uploadvideo':
            /* 上传文件 */
        case 'file':
        case 'uploadfile':
            $result = include("action_upload.php");
            break;

        /* 列出图片 */
        case 'listImage':
        case 'listimage':
            $result = include("action_list.php");
            break;
        /* 列出文件 */
        case 'listFile':
        case 'listfile':
            $result = include("action_list.php");
            break;
        /* 列出视频 */
        case 'listvideo':
            $result = include("action_list.php");
            break;

        /* 抓取远程文件 */
        case 'catch':
        case 'catchimage':
            $result = include("action_crawler.php");
            break;

        default:
            $result = json_encode(array(
                'state'=> '请求地址出错'
            ), JSON_UNESCAPED_UNICODE);
            break;
    }
} elseif ($action == 'config') {
    $result = json_encode($CONFIG);
} else {
    $result = json_encode(array(
        'state'=> $error ? $error : '请登录在操作'
    ), JSON_UNESCAPED_UNICODE);
}

/* 输出结果 */
if (isset($_GET["callback"])) {
    if (preg_match("/^[\w_]+$/", $_GET["callback"])) {
        echo htmlspecialchars($_GET["callback"]) . '(' . $result . ')';
    } else {
        echo json_encode(array(
            'state'=> 'callback参数不合法'
        ), JSON_UNESCAPED_UNICODE);
    }
} else {
    echo $result;
}
exit;
