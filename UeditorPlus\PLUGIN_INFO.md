# UEditorPlus插件信息

## 插件概述

**插件名称**: UEditorPlus编辑器插件  
**版本**: v1.0.3  
**开发日期**: 2025-08-07  
**适用系统**: XunRuiCMS v4.7.0+  
**基于**: UEditorPlus v4.4.0  

## 开发规范遵循

### ✅ XunRuiCMS插件规范
- **目录结构**: 完全按照XunRuiCMS App插件规范
- **Files.txt格式**: 单行逗号分隔的文件路径列表
- **安装方式**: 通过后台应用管理安装，非独立安装脚本
- **字段集成**: 实现标准的字段处理类

### ✅ UEditorPlus官方规范
- **后端API**: 完全符合UEditorPlus官方后端开发文档
- **配置格式**: 按照官方规范实现config.php配置文件
- **返回格式**: 所有API接口返回格式符合官方标准
- **功能支持**: 支持所有UEditorPlus核心功能

## 核心功能

### 📤 上传功能
- **图片上传** (image) - jpg, png, jpeg, gif, bmp, webp
- **视频上传** (video) - mp4, avi, mov等
- **文件上传** (file) - zip, pdf, doc, docx等
- **涂鸦上传** (scrawl) - 画板涂鸦功能
- **截图上传** (snap) - 屏幕截图功能

### 🌐 网络功能
- **远程图片抓取** (catch) - 自动下载远程图片
- **图片列表** (listImage) - 浏览已上传图片
- **文件列表** (listFile) - 浏览已上传文件

### 🎨 高级功能
- **AI辅助** - AI写作辅助功能
- **音频支持** - 音频文件上传和播放
- **内容导入** - Word文档、Markdown导入
- **公式编辑** - LaTeX数学公式支持
- **图表插入** - 各种图表类型
- **模板功能** - 预设内容模板

## 技术架构

### 后端处理
- **统一入口**: `/api/ueditorplus/php/controller.php`
- **配置文件**: `/api/ueditorplus/php/config.php`
- **上传处理**: `action_upload.php`
- **列表处理**: `action_list.php`
- **抓取处理**: `action_crawler.php`
- **上传类**: `Uploader.class.php`

### 前端集成
- **核心文件**: `ueditor.all.js`, `ueditor.config.js`
- **主题样式**: `themes/default/css/ueditor.css`
- **对话框**: `dialogs/` 目录下各功能对话框
- **第三方库**: `third-party/` 目录下依赖库

### 字段集成
- **字段类**: `/dayrui/Fcms/Field/UeditorPlus.php`
- **配置选项**: 编辑器模式、工具栏、上传设置等
- **数据处理**: 输入输出、附件管理、内容过滤

## 安装部署

### 1. 文件上传
将UeditorPlus目录上传到网站根目录

### 2. 插件安装
1. 登录XunRuiCMS后台
2. 进入"应用管理" -> "本地应用"
3. 找到UEditorPlus插件，点击"安装"

### 3. 字段配置
1. 进入字段管理
2. 选择"UEditorPlus"字段类型
3. 配置编辑器参数

### 4. 功能测试
- 编辑器演示: `test.html`
- API接口测试: `api-test.html`

## 配置说明

### 基础配置 (config.php)
```php
// 图片上传配置
"imageMaxSize" => 10485760,
"imageAllowFiles" => [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"],

// 视频上传配置  
"videoMaxSize" => 104857600,
"videoAllowFiles" => [".mp4", ".avi", ".mov"],

// 文件上传配置
"fileMaxSize" => 104857600,
"fileAllowFiles" => [".zip", ".pdf", ".doc", ".docx"],

// 公式配置
"formulaConfig" => [
    "imageUrlTemplate" => "https://r.latexeasy.com/image.svg?{}"
]
```

### 字段配置选项
- **编辑器模式**: 完整/精简/自定义
- **工具栏配置**: 可自定义工具栏按钮
- **上传设置**: 附件配置、水印设置
- **高度设置**: 固定高度或自动伸长

## 兼容性

### 系统要求
- XunRuiCMS v4.7.0+
- PHP 7.0+
- 支持文件上传功能
- 可写的uploads目录

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+
- IE 11+ (部分功能)

## 更新记录

### v1.0.3 (2025-08-07)
- 修正Files.txt格式为XunRuiCMS标准
- 移除错误的install.php安装方式
- 完善文件列表，包含所有UEditorPlus功能

### v1.0.2 (2025-08-07)
- 根据UEditorPlus官方文档优化配置
- 添加snap、scrawl等功能支持
- 创建API测试工具

### v1.0.1 (2025-08-07)
- 重构API处理方式为PHP直接处理
- 修正字段类集成方式
- 完善XunRuiCMS系统集成

### v1.0.0 (2025-08-07)
- 初始版本发布
- 基于UEditorPlus v4.4.0开发
- 实现完整功能集成

## 技术支持

- **UEditorPlus官方**: https://github.com/modstart-lib/ueditor-plus
- **官方文档**: https://open-doc.modstart.com/ueditor-plus
- **XunRuiCMS官网**: https://www.xunruicms.com
- **开源协议**: Apache 2.0
