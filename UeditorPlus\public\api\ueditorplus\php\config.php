<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/* UEditorPlus前后端通信相关的配置 */

return [

    /* 上传图片配置项 */
    "imageAltValue" => "name", /*图片alt属性和title属性填充值：title为内容标题字段值、name为图片名称*/
    "imageActionName" => "image", /* 执行上传图片的action名称 */
    "imageFieldName" => "file", /* 提交的图片表单名称 */
    "imageMaxSize" => 10485760, /* 上传大小限制，单位B，默认10MB */
    "imageAllowFiles" => [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"], /* 上传图片格式显示 */
    "imageCompressEnable" => true, /* 是否压缩图片,默认是true */
    "imageCompressBorder" => 5000, /* 图片压缩最长边限制 */
    "imageInsertAlign" => "none", /* 插入的图片浮动方式 */
    "imageUrlPrefix" => "", /* 图片访问路径前缀 */
    "imagePathFormat" => "/ueditorplus/image/{yyyy}{mm}/{time}{rand:6}", /* 上传保存路径 */

    /* 涂鸦上传配置 */
    "scrawlActionName" => "scrawl", /* 执行上传涂鸦的action名称 */
    "scrawlFieldName" => "file", /* 提交的图片表单名称 */
    "scrawlMaxSize" => 10485760, /* 上传大小限制，单位B */
    "scrawlUrlPrefix" => "", /* 图片访问路径前缀 */
    "scrawlInsertAlign" => "none", /* 插入的图片浮动方式 */
    "scrawlPathFormat" => "/ueditorplus/scrawl/{yyyy}{mm}/{time}{rand:6}",
    "scrawlAllowFiles" => [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 涂鸦允许的文件格式 */

    /* 截图上传配置 */
    "snapscreenActionName" => "snap", /* 执行上传截图的action名称 */
    "snapscreenUrlPrefix" => "", /* 图片访问路径前缀 */
    "snapscreenInsertAlign" => "none", /* 插入的图片浮动方式 */
    "snapscreenPathFormat" => "/ueditorplus/snap/{yyyy}{mm}/{time}{rand:6}",

    /* 图片抓取配置 */
    "catcherActionName" => "catch", /* 执行抓取远程图片的action名称 */
    "catcherFieldName" => "source", /* 提交的图片列表表单名称 */
    "catcherLocalDomain" => ["127.0.0.1", "localhost"], /* 例外的图片抓取域名 */
    "catcherUrlPrefix" => "", /* 图片访问路径前缀 */
    "catcherMaxSize" => 10485760, /* 上传大小限制，单位B */
    "catcherAllowFiles" => [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 抓取图片格式显示 */
    "catcherPathFormat" => "/ueditorplus/catch/{yyyy}{mm}/{time}{rand:6}",

    /* 上传视频配置 */
    "videoActionName" => "video", /* 执行上传视频的action名称 */
    "videoFieldName" => "file", /* 提交的视频表单名称 */
    "videoPathFormat" => "/ueditorplus/video/{yyyy}{mm}/{time}{rand:6}", /* 上传保存路径 */
    "videoUrlPrefix" => "", /* 视频访问路径前缀 */
    "videoMaxSize" => 104857600, /* 上传大小限制，单位B，默认100MB */
    "videoAllowFiles" => [
        ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
        ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], /* 上传视频格式显示 */

    /* 上传文件配置 */
    "fileActionName" => "file", /* 执行上传文件的action名称 */
    "fileFieldName" => "file", /* 提交的文件表单名称 */
    "filePathFormat" => "/ueditorplus/file/{yyyy}{mm}/{time}{rand:6}", /* 上传保存路径 */
    "fileUrlPrefix" => "", /* 文件访问路径前缀 */
    "fileMaxSize" => 104857600, /* 上传大小限制，单位B，默认100MB */
    "fileAllowFiles" => [
        ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
        ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid",
        ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso",
        ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"
    ], /* 上传文件格式显示 */

    /* 列出目录下的图片 */
    "imageManagerActionName" => "listImage", /* 执行图片管理的action名称 */
    "imageManagerListSize" => 20, /* 每次列出文件数量 */
    "imageManagerUrlPrefix" => "", /* 图片访问路径前缀 */
    "imageManagerInsertAlign" => "none", /* 插入的图片浮动方式 */
    "imageManagerAllowFiles" => [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 列出的文件类型 */
    "imageManagerListPath" => "/ueditorplus/image/", /* 指定要列出图片的目录 */

    /* 列出目录下的文件 */
    "showFileExt" => 1, //是否显示文件扩展名，1表示显示，0不显示
    "fileManagerActionName" => "listFile", /* 执行文件管理的action名称 */
    "fileManagerUrlPrefix" => "", /* 文件访问路径前缀 */
    "fileManagerListSize" => 20, /* 每次列出文件数量 */
    "fileManagerAllowFiles" => [
        ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
        ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid",
        ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso",
        ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"
    ], /* 列出的文件类型 */
    "fileManagerListPath" => "/ueditorplus/file/", /* 指定要列出文件的目录 */

    /* 公式配置 */
    "formulaConfig" => [
        "imageUrlTemplate" => "https://r.latexeasy.com/image.svg?{}"
    ]

];
