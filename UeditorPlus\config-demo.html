<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UEditorPlus 配置功能演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            background: #fafafa;
        }
        .feature-card h3 {
            color: #007cba;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        .install-steps {
            background: #e7f3ff;
            border-left: 4px solid #007cba;
            padding: 20px;
            margin: 20px 0;
        }
        .install-steps h3 {
            color: #007cba;
            margin-top: 0;
        }
        .install-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .install-steps li {
            margin: 10px 0;
            line-height: 1.6;
        }
        .config-tabs {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .config-tabs h3 {
            color: #856404;
            margin-top: 0;
        }
        .tab-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .tab-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        .tab-item h4 {
            color: #007cba;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .tab-item p {
            margin: 0;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        .version-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .version-info h3 {
            color: #155724;
            margin: 0 0 10px 0;
        }
        .version-info p {
            margin: 5px 0;
            color: #155724;
        }
        .note {
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
            padding: 15px;
            margin: 20px 0;
        }
        .note h4 {
            color: #495057;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 UEditorPlus 配置功能演示</h1>
        
        <div class="version-info">
            <h3>版本信息</h3>
            <p><strong>当前版本:</strong> v1.0.4</p>
            <p><strong>更新内容:</strong> 新增后台配置管理界面</p>
            <p><strong>发布日期:</strong> 2025-08-07</p>
        </div>

        <div class="install-steps">
            <h3>📋 安装和配置步骤</h3>
            <ol>
                <li><strong>安装插件:</strong> 在后台"应用管理" → "本地应用"中安装UEditorPlus插件</li>
                <li><strong>访问配置:</strong> 在后台左侧菜单找到"UEditorPlus编辑器" → "插件设置"</li>
                <li><strong>配置功能:</strong> 根据需要在四个配置选项卡中进行设置</li>
                <li><strong>使用编辑器:</strong> 在字段管理中选择"UEditorPlus"字段类型</li>
            </ol>
        </div>

        <div class="config-tabs">
            <h3>⚙️ 配置选项卡功能</h3>
            <div class="tab-list">
                <div class="tab-item">
                    <h4>🔧 基础设置</h4>
                    <p>一键开关、百度地图AK配置、自动保存、粘贴过滤、多站同步等基础功能设置</p>
                </div>
                <div class="tab-item">
                    <h4>🚀 高级功能</h4>
                    <p>AI辅助写作、公式编辑、图表插入、模板功能、音频支持等高级功能开关</p>
                </div>
                <div class="tab-item">
                    <h4>📤 上传设置</h4>
                    <p>图片、视频、文件的最大上传尺寸设置，水印功能配置等</p>
                </div>
                <div class="tab-item">
                    <h4>📄 配置文件</h4>
                    <p>查看编辑器配置文件内容，支持语法高亮显示</p>
                </div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎯 新增配置功能</h3>
                <ul class="feature-list">
                    <li>后台可视化配置界面</li>
                    <li>一键启用/禁用编辑器</li>
                    <li>AI功能配置和管理</li>
                    <li>上传参数动态设置</li>
                    <li>多站点文件同步</li>
                    <li>配置文件实时查看</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🔥 核心功能特性</h3>
                <ul class="feature-list">
                    <li>完整UEditorPlus功能支持</li>
                    <li>图片、视频、文件上传</li>
                    <li>AI辅助写作功能</li>
                    <li>LaTeX公式编辑</li>
                    <li>图表插入功能</li>
                    <li>内容模板管理</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>💡 用户体验优化</h3>
                <ul class="feature-list">
                    <li>响应式设计，支持移动端</li>
                    <li>现代化UI界面</li>
                    <li>字体图标替换图片</li>
                    <li>触摸友好操作</li>
                    <li>自动保存功能</li>
                    <li>粘贴内容过滤</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🛠️ 技术特点</h3>
                <ul class="feature-list">
                    <li>完全符合XunRuiCMS规范</li>
                    <li>标准化插件架构</li>
                    <li>完整的API接口</li>
                    <li>兼容现有UEditor</li>
                    <li>无缝升级迁移</li>
                    <li>开源Apache 2.0协议</li>
                </ul>
            </div>
        </div>

        <div class="note">
            <h4>💡 使用提示</h4>
            <p><strong>配置优先级:</strong> 后台配置界面的设置会影响编辑器的行为，建议优先使用后台界面进行配置。</p>
            <p><strong>高级配置:</strong> 如需更详细的配置，可以直接编辑 <code>/api/ueditorplus/php/config.php</code> 文件。</p>
            <p><strong>功能测试:</strong> 可以访问 <code>test.html</code> 和 <code>api-test.html</code> 进行功能测试。</p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p style="color: #666;">
                <strong>UEditorPlus for XunRuiCMS</strong><br>
                基于 UEditorPlus v4.4.0 | 适用于 XunRuiCMS v4.7.0+<br>
                <a href="https://github.com/modstart-lib/ueditor-plus" target="_blank" style="color: #007cba;">UEditorPlus官方项目</a> | 
                <a href="https://www.xunruicms.com" target="_blank" style="color: #007cba;">XunRuiCMS官网</a>
            </p>
        </div>
    </div>
</body>
</html>
