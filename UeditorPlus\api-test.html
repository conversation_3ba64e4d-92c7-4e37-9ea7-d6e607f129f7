<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UEditorPlus API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        input[type="text"] {
            width: 300px;
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UEditorPlus API接口测试</h1>
        <p>测试UEditorPlus插件的各个API接口是否正常工作</p>

        <!-- 配置获取测试 -->
        <div class="test-section">
            <h3>1. 配置获取测试 (config)</h3>
            <p>测试获取编辑器配置信息</p>
            <button onclick="testConfig()">获取配置</button>
            <div id="config-result" class="result"></div>
        </div>

        <!-- 图片上传测试 -->
        <div class="test-section">
            <h3>2. 图片上传测试 (image)</h3>
            <p>测试图片文件上传功能</p>
            <input type="file" id="image-file" accept="image/*">
            <button onclick="testImageUpload()">上传图片</button>
            <div id="image-result" class="result"></div>
        </div>

        <!-- 视频上传测试 -->
        <div class="test-section">
            <h3>3. 视频上传测试 (video)</h3>
            <p>测试视频文件上传功能</p>
            <input type="file" id="video-file" accept="video/*">
            <button onclick="testVideoUpload()">上传视频</button>
            <div id="video-result" class="result"></div>
        </div>

        <!-- 文件上传测试 -->
        <div class="test-section">
            <h3>4. 文件上传测试 (file)</h3>
            <p>测试普通文件上传功能</p>
            <input type="file" id="file-file">
            <button onclick="testFileUpload()">上传文件</button>
            <div id="file-result" class="result"></div>
        </div>

        <!-- 远程图片抓取测试 -->
        <div class="test-section">
            <h3>5. 远程图片抓取测试 (catch)</h3>
            <p>测试抓取远程图片功能</p>
            <input type="text" id="remote-url" placeholder="输入图片URL" value="https://via.placeholder.com/300x200.png">
            <button onclick="testCatchImage()">抓取图片</button>
            <div id="catch-result" class="result"></div>
        </div>

        <!-- 图片列表测试 -->
        <div class="test-section">
            <h3>6. 图片列表测试 (listImage)</h3>
            <p>测试获取图片列表功能</p>
            <button onclick="testListImage()">获取图片列表</button>
            <div id="listimage-result" class="result"></div>
        </div>

        <!-- 文件列表测试 -->
        <div class="test-section">
            <h3>7. 文件列表测试 (listFile)</h3>
            <p>测试获取文件列表功能</p>
            <button onclick="testListFile()">获取文件列表</button>
            <div id="listfile-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/ueditorplus/php/controller.php';

        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
        }

        function testConfig() {
            fetch(API_BASE + '?action=config')
                .then(response => response.json())
                .then(data => {
                    showResult('config-result', data, true);
                })
                .catch(error => {
                    showResult('config-result', {error: error.message}, false);
                });
        }

        function testImageUpload() {
            const fileInput = document.getElementById('image-file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('image-result', {error: '请选择图片文件'}, false);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            fetch(API_BASE + '?action=image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('image-result', data, data.state === 'SUCCESS');
            })
            .catch(error => {
                showResult('image-result', {error: error.message}, false);
            });
        }

        function testVideoUpload() {
            const fileInput = document.getElementById('video-file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('video-result', {error: '请选择视频文件'}, false);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            fetch(API_BASE + '?action=video', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('video-result', data, data.state === 'SUCCESS');
            })
            .catch(error => {
                showResult('video-result', {error: error.message}, false);
            });
        }

        function testFileUpload() {
            const fileInput = document.getElementById('file-file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('file-result', {error: '请选择文件'}, false);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            fetch(API_BASE + '?action=file', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('file-result', data, data.state === 'SUCCESS');
            })
            .catch(error => {
                showResult('file-result', {error: error.message}, false);
            });
        }

        function testCatchImage() {
            const url = document.getElementById('remote-url').value;
            
            if (!url) {
                showResult('catch-result', {error: '请输入图片URL'}, false);
                return;
            }

            const formData = new FormData();
            formData.append('source[]', url);

            fetch(API_BASE + '?action=catch', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult('catch-result', data, data.state === 'SUCCESS');
            })
            .catch(error => {
                showResult('catch-result', {error: error.message}, false);
            });
        }

        function testListImage() {
            fetch(API_BASE + '?action=listImage')
                .then(response => response.json())
                .then(data => {
                    showResult('listimage-result', data, data.state === 'SUCCESS');
                })
                .catch(error => {
                    showResult('listimage-result', {error: error.message}, false);
                });
        }

        function testListFile() {
            fetch(API_BASE + '?action=listFile')
                .then(response => response.json())
                .then(data => {
                    showResult('listfile-result', data, data.state === 'SUCCESS');
                })
                .catch(error => {
                    showResult('listfile-result', {error: error.message}, false);
                });
        }

        // 页面加载时自动测试配置获取
        window.onload = function() {
            testConfig();
        };
    </script>
</body>
</html>
