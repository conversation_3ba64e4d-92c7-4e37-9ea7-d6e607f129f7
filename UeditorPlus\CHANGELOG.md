# UEditorPlus插件更新日志

## v1.0.3 (2025-08-07)

### 🔧 插件规范修正
- **修正Files.txt格式**: 按照XunRuiCMS插件规范，将Files.txt改为单行逗号分隔格式
- **移除错误安装方式**: 删除install.php文件，XunRuiCMS插件通过后台应用管理安装
- **完善文件列表**: 在Files.txt中包含所有必要的前端和后端文件路径

### 📋 文档更新
- **更新README**: 移除关于install.php的错误信息
- **完善目录结构**: 更新文档中的目录结构说明
- **添加测试页面**: 在文档中说明test.html和api-test.html的用途

## v1.0.2 (2025-08-07)

### 🎯 官方规范配置
- **完善配置文件**: 根据UEditorPlus官方后端开发文档完善配置项
- **添加缺失功能**: 增加对snap（截图）和scrawl（涂鸦）功能的完整支持
- **修正配置错误**: 修正scrawlActionName等配置项的错误
- **完善路径配置**: 添加imageManagerListPath、fileManagerListPath等路径配置

### 🧪 测试工具
- **API测试页面**: 新增api-test.html，可测试所有API接口功能
- **功能验证**: 提供完整的功能测试和配置验证工具
- **错误诊断**: 详细的错误信息和成功状态显示

### 📋 规范遵循
- **官方文档**: 完全按照UEditorPlus官方后端开发文档实现
- **返回格式**: 确保所有API返回格式符合官方规范
- **功能完整**: 支持config、image、video、file、catch、listImage、listFile等所有接口

## v1.0.1 (2025-08-07)

### 🔧 架构修正
- **重构API处理方式**: 改为使用PHP文件直接处理，符合XunRuiCMS Ueditor插件规范
- **修正路由配置**: 移除不必要的控制器路由，使用 `/api/ueditorplus/php/controller.php` 作为统一入口
- **完善字段类集成**: 按照原Ueditor字段类的规范重新实现
- **修正前端配置**: 更新serverUrl指向正确的PHP处理文件

### 📋 技术改进
- 采用与原Ueditor插件相同的架构模式
- 复用XunRuiCMS的上传处理逻辑
- 保持与原Ueditor插件的兼容性
- 简化插件结构，移除不必要的控制器

## v1.0.0 (2025-08-07)

### 🎉 首次发布
- 基于UEditorPlus v4.4.0开发
- 适配XunRuiCMS v4.7.0+
- 完整的插件架构实现

### ✨ 核心功能
- **字段类型**: 实现UEditorPlus字段类，完全兼容XunRuiCMS字段系统
- **后端API**: 完整实现UEditorPlus后端API规范，采用PHP直接处理方式
  - 配置获取 (config)
  - 图片上传 (image)
  - 视频上传 (video)
  - 文件上传 (file)
  - 远程图片抓取 (catch)
  - 图片列表 (listImage)
  - 文件列表 (listFile)
- **前端集成**: 集成UEditorPlus完整前端资源
- **架构规范**: 完全按照XunRuiCMS Ueditor插件开发规范实现

### 🖼️ 图片处理
- 支持多种图片格式：jpg, png, jpeg, gif, bmp
- 图片大小限制配置
- 水印功能支持
- 远程图片自动抓取和本地化

### 🎥 多媒体支持
- 视频上传：mp4, avi, mov
- 文件上传：zip, pdf, doc, docx
- 文件大小限制配置
- 文件列表管理

### 🔧 系统集成
- 完全兼容XunRuiCMS插件规范
- 支持XunRuiCMS权限系统
- 集成XunRuiCMS上传服务
- 支持XunRuiCMS附件管理

### 📱 用户体验
- 响应式设计，完美支持移动端
- 现代化UI界面
- 字体图标替代图片图标
- 多种编辑器模式（完整/精简/自定义）

### 🛠️ 开发特性
- 完整的错误处理机制
- 详细的日志记录
- 安全的文件上传验证
- 灵活的配置系统

### 📁 文件结构
```
UeditorPlus/
├── dayrui/
│   ├── App/UeditorPlus/
│   │   └── Config/              # 插件配置文件
│   └── Fcms/Field/
│       └── UeditorPlus.php     # 字段处理类
├── public/api/ueditorplus/     # 前端资源
│   └── php/                    # 后端PHP处理文件
├── README.md                   # 说明文档
├── CHANGELOG.md               # 更新日志
├── install.txt                # 安装说明
├── install.php               # 安装检查脚本
└── test.html                 # 测试页面
```

### 🔗 相关链接
- UEditorPlus官方：https://github.com/modstart-lib/ueditor-plus
- UEditorPlus文档：https://open-doc.modstart.com/ueditor-plus
- XunRuiCMS官网：https://www.xunruicms.com

### 📋 系统要求
- XunRuiCMS v4.7.0+
- PHP 7.0+
- 支持文件上传功能
- 可写的uploads目录

### 🚀 安装方法
1. 上传插件文件到网站根目录
2. 后台应用管理中安装插件
3. 配置插件参数
4. 在字段管理中使用UEditorPlus字段类型

### 🎯 下一版本计划
- [ ] 增加更多文件格式支持
- [ ] 优化图片压缩算法
- [ ] 增加批量文件管理功能
- [ ] 支持云存储集成
- [ ] 增加更多编辑器插件

---

**开发者**: 基于UEditorPlus开发  
**许可证**: Apache 2.0  
**发布日期**: 2025-08-07
