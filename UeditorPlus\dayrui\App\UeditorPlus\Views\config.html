{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light myfbody">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('基础设置')} </a>
                </li>
                <li class="{if $page==1}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-magic"></i> {dr_lang('高级功能')} </a>
                </li>
                <li class="{if $page==2}active{/if}">
                    <a href="#tab_2" data-toggle="tab" onclick="$('#dr_page').val('2')"> <i class="fa fa-upload"></i> {dr_lang('上传设置')} </a>
                </li>
                <li class="{if $page==3}active{/if}">
                    <a href="#tab_3" data-toggle="tab" onclick="$('#dr_page').val('3')"> <i class="fa fa-file-code-o"></i> {dr_lang('配置文件')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">
                <!-- 基础设置 -->
                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        
                        <div class="form-group">
                            <label class="col-md-2 control-label">一键开关</label>
                            <div class="col-md-9">
                                <label><button type="button" onclick="dr_iframe_show('一键启用', '{dr_url(\"ueditorplus/config/set_index\")}&id=1')" class="btn dark"> <i class="fa fa-th-large"></i> {dr_lang('设置全站使用UEditorPlus编辑器')} </button></label>
                                <label><button type="button" onclick="dr_iframe_show('一键还原', '{dr_url(\"ueditorplus/config/set_index\")}&id=0')" class="btn blue"> <i class="fa fa-th-large"></i> {dr_lang('全站还原为系统编辑器')} </button></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('百度地图Api-AK')}</label>
                            <div class="col-md-5">
                                <label><input class="form-control input-large" id="bdak" type="text" name="data[ak]" value="{htmlspecialchars((string)$data['ak'])}"  ></label>
                                <label><a class="btn btn-sm blue" href="javascript:dr_help(584);"> {dr_lang('立即申请')} </a></label>
                                <span class="help-block">{dr_lang('用于百度地图调用')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('自动保存')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[auto_save]" value="1" {if $data['auto_save']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[auto_save]" value="0" {if !$data['auto_save']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('编辑器内容自动保存功能')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('粘贴过滤')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[paste_filter]" value="1" {if $data['paste_filter']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[paste_filter]" value="0" {if !$data['paste_filter']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('粘贴内容时自动过滤格式')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('同步到多个站')}</label>
                            <div class="col-md-5">
                                 <label><a class="btn btn-sm blue" href="javascript:dr_sync_files();"> {dr_lang('立即同步')} </a></label>
                                   <span class="help-block">{dr_lang('将UEditorPlus编辑器文件同步到多站域名下的api目录中')}</span>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 高级功能 -->
                <div class="tab-pane {if $page==1}active{/if}" id="tab_1">
                    <div class="form-body">
                        
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('AI辅助功能')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ai_enabled]" value="1" {if $data['ai_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ai_enabled]" value="0" {if !$data['ai_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('AI写作辅助功能')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('AI API密钥')}</label>
                            <div class="col-md-5">
                                <input class="form-control input-large" type="text" name="data[ai_api_key]" value="{htmlspecialchars((string)$data['ai_api_key'])}" placeholder="请输入AI服务API密钥">
                                <span class="help-block">{dr_lang('用于AI功能调用的API密钥')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('公式编辑')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[formula_enabled]" value="1" {if $data['formula_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[formula_enabled]" value="0" {if !$data['formula_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('LaTeX数学公式编辑功能')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('图表插入')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[chart_enabled]" value="1" {if $data['chart_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[chart_enabled]" value="0" {if !$data['chart_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('各种图表类型插入功能')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('模板功能')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[template_enabled]" value="1" {if $data['template_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[template_enabled]" value="0" {if !$data['template_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('预设内容模板功能')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('音频支持')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[audio_enabled]" value="1" {if $data['audio_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[audio_enabled]" value="0" {if !$data['audio_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('音频文件上传和播放功能')}</span>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 上传设置 -->
                <div class="tab-pane {if $page==2}active{/if}" id="tab_2">
                    <div class="form-body">
                        
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('图片最大尺寸')}</label>
                            <div class="col-md-5">
                                <div class="input-group">
                                    <input class="form-control" type="number" name="data[image_max_size]" value="{$data['image_max_size']}" min="1" max="100">
                                    <span class="input-group-addon">MB</span>
                                </div>
                                <span class="help-block">{dr_lang('单个图片文件最大上传尺寸')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('视频最大尺寸')}</label>
                            <div class="col-md-5">
                                <div class="input-group">
                                    <input class="form-control" type="number" name="data[video_max_size]" value="{$data['video_max_size']}" min="1" max="500">
                                    <span class="input-group-addon">MB</span>
                                </div>
                                <span class="help-block">{dr_lang('单个视频文件最大上传尺寸')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('文件最大尺寸')}</label>
                            <div class="col-md-5">
                                <div class="input-group">
                                    <input class="form-control" type="number" name="data[file_max_size]" value="{$data['file_max_size']}" min="1" max="200">
                                    <span class="input-group-addon">MB</span>
                                </div>
                                <span class="help-block">{dr_lang('单个文件最大上传尺寸')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('水印功能')}</label>
                            <div class="col-md-5">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[watermark_enabled]" value="1" {if $data['watermark_enabled']}checked{/if}> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[watermark_enabled]" value="0" {if !$data['watermark_enabled']}checked{/if}> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('图片上传时自动添加水印')}</span>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 配置文件 -->
                <div class="tab-pane {if $page==3}active{/if}" id="tab_3">
                    <div class="form-body">
                        
                        <link href="{THEME_PATH}assets/global/plugins/codemirror/lib/codemirror.css" rel="stylesheet" type="text/css" />
                        <link href="{THEME_PATH}assets/global/plugins/codemirror/theme/neat.css" rel="stylesheet" type="text/css" />
                        <script src="{THEME_PATH}assets/global/plugins/codemirror/lib/codemirror.js" type="text/javascript"></script>
                        <script src="{THEME_PATH}assets/global/plugins/codemirror/mode/javascript/javascript.js" type="text/javascript"></script>
                        <script src="{THEME_PATH}assets/global/plugins/codemirror/addon/display/autorefresh.js" type="text/javascript"></script>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('编辑器配置文件')}</label>
                            <div class="col-md-9" style="color:green" >
                                <div class="well well2">
                                   /api/ueditorplus/php/config.php
                                </div>
                                <textarea id="file_code_ueditorplus" class="form-control">{if file_exists(ROOTPATH.'api/ueditorplus/php/config.php')}{dr_code2html(file_get_contents(ROOTPATH.'api/ueditorplus/php/config.php'))}{else}配置文件不存在，请检查UEditorPlus是否正确安装{/if}</textarea>
                                <div class="note note-danger margin-top-30">
                                    {dr_lang('若需变更此类参数，需要在服务器中找到此文件做对应的修改操作')}
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="portlet-body form myfooter">
            <div class="form-actions text-center">
                <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
            </div>
        </div>
     
    </div>
</form>

<script type="text/javascript">
    jQuery(document).ready(function() {
        var myTextArea = document.getElementById('file_code_ueditorplus');
        if (myTextArea) {
            var myCodeMirror = CodeMirror.fromTextArea(myTextArea, {
                lineNumbers: true,
                matchBrackets: true,
                styleActiveLine: true,
                theme: "neat",
                mode: 'javascript',
                lint: true,
                readOnly: true,
                autoRefresh: true
            });
            myCodeMirror.setSize(null, 300);
        }
    });
    
    function dr_sync_files() {
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "{dr_url('ueditorplus/config/sync_files')}",
            success: function (json) {
                dr_tips(json.code, json.msg)
            },
            error: function(HttpRequest, ajax, thrownError) {
                dr_ajax_alert_error(HttpRequest, ajax, thrownError);
            }
        });
    }
</script>

{template "footer.html"}
