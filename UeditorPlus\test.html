<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UEditorPlus插件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .editor-container {
            margin: 20px 0;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .feature h4 {
            margin-top: 0;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UEditorPlus编辑器插件测试</h1>
        
        <div class="info">
            <h3>插件信息</h3>
            <p><strong>版本：</strong>1.0.0</p>
            <p><strong>基于：</strong>UEditorPlus v4.4.0</p>
            <p><strong>适用于：</strong>XunRuiCMS v4.7.0+</p>
            <p><strong>开发时间：</strong>2025-08-07</p>
        </div>

        <div class="features">
            <div class="feature">
                <h4>✨ 全新UI外观</h4>
                <p>使用字体图标替换原有图片图标，界面更加现代化</p>
            </div>
            <div class="feature">
                <h4>📄 文档导入</h4>
                <p>支持Word文档（docx）、Markdown文档（md）一键导入</p>
            </div>
            <div class="feature">
                <h4>🖼️ 图片处理</h4>
                <p>支持图片上传、水印、压缩等功能</p>
            </div>
            <div class="feature">
                <h4>🎥 多媒体支持</h4>
                <p>支持视频、音频、文件上传和管理</p>
            </div>
            <div class="feature">
                <h4>📱 响应式设计</h4>
                <p>完美支持移动端，适配各种屏幕尺寸</p>
            </div>
            <div class="feature">
                <h4>🔧 兼容性强</h4>
                <p>兼容现有UEditor，实现无缝切换</p>
            </div>
        </div>

        <div class="editor-container">
            <h3>编辑器演示</h3>
            <script id="editor" type="text/plain" style="height:400px;">
                <h2>欢迎使用UEditorPlus编辑器！</h2>
                <p>这是一个基于UEditorPlus开发的XunRuiCMS插件，具有以下特性：</p>
                <ul>
                    <li><strong>全新UI外观</strong>：使用字体图标，界面更加美观</li>
                    <li><strong>文档导入</strong>：支持Word、Markdown文档导入</li>
                    <li><strong>图片处理</strong>：支持图片上传、水印、压缩</li>
                    <li><strong>多媒体支持</strong>：支持视频、音频文件</li>
                    <li><strong>响应式设计</strong>：完美支持移动端</li>
                </ul>
                <p>您可以尝试以下功能：</p>
                <ol>
                    <li>插入图片、视频、音频</li>
                    <li>使用各种文本格式化工具</li>
                    <li>插入表格、链接、公式</li>
                    <li>使用代码高亮功能</li>
                </ol>
                <blockquote>
                    <p>UEditorPlus让富文本编辑更加简单高效！</p>
                </blockquote>
            </script>
        </div>

        <div class="info">
            <h3>安装说明</h3>
            <ol>
                <li>将UeditorPlus目录上传到网站根目录</li>
                <li>在后台应用管理中安装UEditorPlus插件</li>
                <li>在字段管理中选择UEditorPlus字段类型</li>
                <li>配置相关参数即可使用</li>
            </ol>
        </div>
    </div>

    <!-- UEditorPlus资源文件 -->
    <script type="text/javascript" src="public/api/ueditorplus/ueditor.config.js"></script>
    <script type="text/javascript" src="public/api/ueditorplus/ueditor.all.js"></script>
    
    <script type="text/javascript">
        // 初始化编辑器
        var ue = UE.getEditor('editor', {
            // 编辑器配置
            initialFrameWidth: '100%',
            initialFrameHeight: 400,
            autoHeightEnabled: false,
            toolbars: [[
                'fullscreen', 'source', '|',
                'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|',
                'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                'directionalityltr', 'directionalityrtl', 'indent', '|',
                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|',
                'link', 'unlink', 'anchor', '|',
                'insertimage', 'emotion', 'scrawl', 'snapscreen', 'insertvideo', 'attachment', 'insertframe', 'insertcode', 'template', '|',
                'horizontal', 'date', 'time', 'spechars', 'formula', '|',
                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', '|',
                'print', 'preview', 'searchreplace'
            ]],
            // 服务器配置
            serverUrl: '/api/ueditorplus/php/controller.php',
            // 其他配置
            charset: 'utf-8',
            lang: 'zh-cn'
        });
    </script>
</body>
</html>
