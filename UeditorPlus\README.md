# UEditorPlus编辑器插件

基于UEditorPlus v4.4.0开发的XunRuiCMS富文本编辑器插件，让UEditor重新焕发活力。

## 功能特性

### ✨ 全新UI外观
- 使用字体图标替换原有图片图标
- 现代化的界面设计
- 更好的用户体验

### 📄 文档导入支持
- 支持Word文档（docx）一键导入
- 支持Markdown文档（md）导入
- 保持原有格式和样式

### 🖼️ 图片处理增强
- 图片上传、压缩、水印
- 支持图片管理和浏览
- 远程图片抓取功能

### 🎥 多媒体支持
- 视频上传和播放
- 音频文件支持
- 文件附件管理

### 📱 响应式设计
- 完美支持移动端
- 适配各种屏幕尺寸
- 触摸友好的操作界面

### 🔧 兼容性强
- 兼容现有UEditor功能
- 无缝切换，平滑升级
- 完全符合UEditorPlus官方后端规范
- 支持所有UEditorPlus功能（涂鸦、截图、公式等）

### ⚙️ 后台配置管理
- 提供完整的后台配置界面
- 支持基础设置、高级功能、上传设置等配置
- 一键开关功能，快速启用/禁用编辑器
- AI辅助、公式编辑、图表插入等高级功能配置

## 系统要求

- XunRuiCMS v4.7.0+
- PHP 7.0+
- 支持文件上传功能

## 安装方法

### 1. 上传插件文件
将UeditorPlus目录上传到网站根目录

### 2. 安装插件
在后台 `应用管理` -> `本地应用` 中找到UEditorPlus插件，点击安装

### 3. 配置插件

#### 后台配置界面（推荐）
安装完成后，在后台左侧菜单找到 `UEditorPlus编辑器` -> `插件设置`，提供四个配置选项卡：

- **基础设置**: 一键开关、百度地图AK、自动保存、粘贴过滤等
- **高级功能**: AI辅助、公式编辑、图表插入、模板功能、音频支持等
- **上传设置**: 图片/视频/文件大小限制、水印功能等
- **配置文件**: 查看编辑器配置文件内容

#### 文件配置（高级用户）
也可以直接编辑 `/api/ueditorplus/php/config.php` 文件进行配置：
- 图片上传配置（大小、格式、压缩等）
- 视频上传配置
- 文件上传配置
- 涂鸦和截图配置
- 远程图片抓取配置
- 公式渲染配置

### 4. 使用插件
在字段管理中选择 `UEditorPlus` 字段类型，配置相关参数即可使用

## 配置说明

### 基础配置
插件配置文件位于 `/api/ueditorplus/php/config.php`，包含以下配置项：

- **图片上传**: imageMaxSize, imageAllowFiles, imageCompressEnable 等
- **视频上传**: videoMaxSize, videoAllowFiles 等
- **文件上传**: fileMaxSize, fileAllowFiles 等
- **涂鸦功能**: scrawlMaxSize, scrawlAllowFiles 等
- **截图功能**: snapscreenActionName 等
- **远程抓取**: catcherMaxSize, catcherAllowFiles 等
- **公式渲染**: formulaConfig 配置

### 支持的功能
- **图片处理**: 上传、压缩、水印、远程抓取
- **多媒体**: 视频、音频文件上传
- **文档支持**: Word、PDF、压缩包等
- **交互功能**: 涂鸦、截图、公式编辑
- **文件管理**: 图片列表、文件列表浏览

## API接口

插件提供完整的UEditorPlus后端API支持：

### 配置获取
```
GET /api/ueditorplus/php/controller.php?action=config
```

### 文件上传
```
POST /api/ueditorplus/php/controller.php?action=image  # 图片上传
POST /api/ueditorplus/php/controller.php?action=video  # 视频上传
POST /api/ueditorplus/php/controller.php?action=file   # 文件上传
```

### 远程图片抓取
```
POST /api/ueditorplus/php/controller.php?action=catch
```

### 文件列表
```
GET /api/ueditorplus/php/controller.php?action=listImage  # 图片列表
GET /api/ueditorplus/php/controller.php?action=listFile   # 文件列表
```

## 使用示例

### 在模板中使用
```html
<script id="editor" type="text/plain" style="height:400px;"></script>
<script type="text/javascript" src="/api/ueditorplus/ueditor.config.js"></script>
<script type="text/javascript" src="/api/ueditorplus/ueditor.all.js"></script>
<script>
    var ue = UE.getEditor('editor', {
        initialFrameWidth: '100%',
        initialFrameHeight: 400,
        serverUrl: '/api/ueditorplus/php/controller.php'
    });
</script>
```

### 字段配置
在XunRuiCMS字段管理中：
1. 选择字段类型为 `UEditorPlus`
2. 配置编辑器模式（完整/精简/自定义）
3. 设置工具栏按钮
4. 配置上传参数

### 功能测试
- **编辑器测试**: 访问 `test.html` 查看编辑器完整功能演示
- **API测试**: 访问 `api-test.html` 测试各个API接口功能
- **配置验证**: 通过API测试页面验证配置是否正确

## 目录结构

```
UeditorPlus/
├── dayrui/
│   ├── App/
│   │   └── UeditorPlus/
│   │       └── Config/          # 插件配置
│   └── Fcms/
│       └── Field/
│           └── UeditorPlus.php  # 字段处理类
├── public/
│   └── api/
│       └── ueditorplus/        # 前端资源文件
│           └── php/            # 后端PHP处理文件
├── install.txt                 # 安装说明
├── README.md                   # 说明文档
├── test.html                   # 编辑器功能演示
└── api-test.html               # API接口测试
```

## 更新日志

### v1.0.4 (2025-08-07)
- 新增后台配置管理界面
- 支持基础设置、高级功能、上传设置等配置
- 添加一键开关功能
- 完善菜单和路由配置
- 优化用户体验

### v1.0.0 (2025-08-07)
- 初始版本发布
- 基于UEditorPlus v4.4.0开发
- 支持XunRuiCMS v4.7.0+
- 实现完整的后端API接口
- 支持图片、视频、文件上传
- 支持远程图片抓取
- 响应式设计，支持移动端

## 技术支持

- 基于UEditorPlus开源项目：https://github.com/modstart-lib/ueditor-plus
- UEditorPlus文档：https://open-doc.modstart.com/ueditor-plus
- XunRuiCMS官网：https://www.xunruicms.com

## 开源协议

本插件遵循Apache 2.0开源协议

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！
